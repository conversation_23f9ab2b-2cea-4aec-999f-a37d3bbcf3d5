'use client';

import Image from "next/image";
import { features } from "./features";

export default function WhyChooseUs() {
  return (
    <section className="relative py-20 px-4 overflow-hidden bg-white">

      <div
        className="absolute left-20 right-0 top-0 bottom-0 z-0 opacity-50 bg-[url('/grid.png')] bg-repeat bg-[length:300px] bg-[position:center]"
        aria-hidden="true"
      ></div>

      {/* Main content wrapper */}
      <div className="relative z-20 max-w-[1200px] mx-auto text-center px-4 md:pl-[90px] lg:pl-[180px] lg:pr-[120px]">
        <p className="text-[#05A0E2] text-[14px] font-medium tracking-[0.61px] uppercase leading-[17.5px] text-center" style={{ fontFamily: 'Inter' }}>WHY CHOOSE US</p>
        <h2 className="text-[32px] font-normal text-black mt-2 mb-12 leading-[40px] tracking-[0%] text-center" style={{ fontFamily: 'Inter' }}>
          The Prolytech Edge
        </h2>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-14 place-items-center sm:place-items-stretch">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl shadow-md overflow-hidden transition hover:shadow-lg flex flex-col w-full max-w-[330px]"
            >
              <div className="w-full h-[230px] overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.title}
                  className="w-full h-full object-cover"
                  width={330}
                  height={230}
                />
              </div>
              <div className="p-5 text-left flex flex-col gap-3">
                <h3 className="text-lg font-semibold text-gray-900 leading-tight">
                  {feature.title}
                </h3>
                <p className="text-xs text-gray-500 leading-relaxed line-clamp-2 text-left">
                  {feature.desc}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}