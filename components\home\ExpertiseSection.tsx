'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { socialServices } from './socialServices';

export default function ExpertiseSection() {
  const router = useRouter();
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  const handleToggle = (index: number) => {
    setActiveIndex(index === activeIndex ? null : index);
  };

  return (
    <section className="relative bg-white w-full">
      {/* Content adjusted for sticky navbar */}

      {/* Content with Responsive Left Padding */}
      <div className="relative z-10 max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-12 px-4 sm:px-6 py-16 md:pl-24 lg:pl-32">
        {/* LEFT PANEL */}
        <div className="space-y-4 max-w-md">
          <p className="text-[#05A0E2] text-[14px] font-medium tracking-[0.61px] uppercase leading-[17.5px]" style={{ fontFamily: 'Inter' }}>
            Areas of Expertise
          </p>
          <h2 className="text-[32px] font-normal text-black leading-[40px] tracking-[0%]" style={{ fontFamily: 'Inter' }}>
            Our Expertise Across <br /> Domains
          </h2>
          <p className="text-[12px] leading-[20px] font-normal tracking-[0%]" style={{ color: '#8F8F8F', fontFamily: 'Inter' }}>
  <span className="block">Prolytech Solutions is a modern technology company focused on</span>
  <span className="block">crafting scalable digital ecosystems. Our team specializes in building</span>
  <span className="block">cloud-native applications and secure infrastructures for longevity.</span>
</p>

          <button
            onClick={() => router.push('/services')}
            className="inline-flex items-center justify-start gap-[12px] border border-[#BFDBFF] text-blue-500 text-sm font-medium rounded-full"
            style={{
              width: 'fit-content',
              height: '30px',
              paddingTop: '6px',
              paddingBottom: '6px',
              paddingLeft: '20px',
              paddingRight: '20px'
            }}
          >
            View All Services <span className="text-lg">→</span>
          </button>
        </div>

        {/* RIGHT PANEL - LINE FORMAT */}
        <div className="w-full mt-4">
          {socialServices.map((item, index) => (
            <div
              key={index}
              className="border-b border-gray-100 py-5 cursor-pointer transition-all duration-300 ease-in-out"
              onClick={() => handleToggle(index)}
            >
              <div className="flex justify-between items-center">
                <span className="font-semibold text-black text-base leading-tight transition-colors duration-200">{item.title}</span>
                <span className="text-xl text-gray-500 select-none transition-transform duration-300 ease-in-out">
                  {activeIndex === index ? '⌄' : '›'}
                </span>
              </div>
              <div
                className={`overflow-hidden transition-all duration-500 ease-in-out ${
                  activeIndex === index
                    ? 'max-h-96 opacity-100 mt-4 pt-2'
                    : 'max-h-0 opacity-0 mt-0 pt-0'
                }`}
              >
                <p className="text-gray-600 text-sm leading-relaxed max-w-lg">{item.description}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}