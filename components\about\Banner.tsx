'use client';

import React from 'react';
import { ArrowRight } from 'lucide-react';
import Link from 'next/link';

const CallToAction = () => {
  return (
    <section className="bg-[#2B7FFF] text-white py-10 sm:py-16 md:py-20 flex flex-col items-center justify-center text-center px-3 sm:px-4 md:px-8 lg:px-20">
      <h2
        className="text-[22px] sm:text-[26px] md:text-[30px] font-semibold leading-[32px] sm:leading-[36px] md:leading-[40px] tracking-[0%] mb-3 sm:mb-4 relative z-20"
        style={{ fontFamily: "Inter", color: "#FFFFFF" }}
      >
        Ready to Work Together?
      </h2>

      <p
        className="max-w-[98%] sm:max-w-2xl mx-auto mb-5 sm:mb-6 relative z-20 text-[13px] sm:text-[14px] font-normal leading-[22px] sm:leading-[24.5px] tracking-[0%] text-center"
        style={{ fontFamily: "Inter", color: "rgba(255, 255, 255, 0.9)" }}
      >
        Let&apos;s discuss how we can help you build your next digital product or
        <br className="hidden sm:block" />
       transform your existing systems.
      </p>

      <Link
        href="/contact"
        className="inline-flex items-center justify-center gap-2 bg-white text-[#4A90E2] rounded-full relative z-20 w-[140px] h-[38px] sm:w-[168px] sm:h-[42px] text-[13px] sm:text-[14px] shadow-md hover:shadow-lg hover:bg-[#e6f0ff] transition-all duration-200"
        style={{
          fontFamily: "Inter",
          fontWeight: "400",
          lineHeight: "17.5px",
          letterSpacing: "0%",
        }}
      >
        Get in Touch
        <span className="text-lg">→</span>
      </Link>
    </section>
  );
};

export default CallToAction;