'use client';

import React from 'react';
import { values } from './values';

export const ValuesSection: React.FC = () => {
	return (
		<section className="bg-black text-white pt-14 pb-14 md:py-16 px-3 md:px-8 lg:px-20 font-sans">
			<div className="max-w-full md:max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12 ml-0 md:ml-45">
				<div className="mb-6 md:mb-0">
					<h4 className="text-[12px] md:text-[12.3px] font-semibold uppercase tracking-[0.61px] text-left leading-[18px] md:leading-normal" style={{ color: '#05A0E2' }}>
						Our Principles
					</h4>
					<h2 className="text-[20px] md:text-[32px] font-semibold mt-1 md:mt-2 text-left leading-[28px] md:leading-[40px]">
						Values We Live By
					</h2>
				</div>

				<div className="space-y-7 md:space-y-8 mr-0 md:mr-15">
					{values.map((item, index) => (
						<div key={index} className="flex items-start gap-3 md:gap-4">
							<div className="text-white text-xl md:text-2xl mt-1 leading-[22px] md:leading-[28px]">–</div>
							<div>
								<h3 className="font-semibold text-white text-[16px] md:text-[18px] leading-[22px] md:leading-[26px]">
									{item.title}
								</h3>
								<p className="text-gray-400 text-[12px] md:text-[11px] mt-2 md:mt-1 leading-[19px] md:leading-[17px] line-clamp-4 md:line-clamp-2">
									{item.description}
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</section>
	);
};