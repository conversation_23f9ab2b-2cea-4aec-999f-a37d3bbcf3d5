'use client';

import Image from 'next/image';
import Link from 'next/link';
import AboutBg from '../icons/aboutbg'; // Correct path to your SVG component

const AboutHero = () => {
  return (
    <div className="w-full min-h-screen bg-white relative flex">
      
      {/* Background Grid from public folder */}
      <div className="absolute inset-0 z-0 bg-[url('/grid.png')] bg-repeat opacity-60"></div>

      {/* Left Navigation Sidebar - Hidden on small and medium screens, visible on large screens */}
      <div className="fixed left-0 top-0 h-full w-16 bg-black flex-col items-center justify-center space-y-8 z-10 hidden lg:flex">
        <div className="w-6 h-6 bg-cyan-400 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
        <div className="w-6 h-6 bg-gray-600 rounded-sm"></div>
      </div>

      {/* Main Content Area - Adjusted margin for small/medium screens */}
      <div className="flex-1 ml-0 lg:ml-16 bg-transparent relative overflow-hidden">

        {/* Logo - Responsive positioning */}
        <div className="absolute top-4 left-4 md:left-[250px] lg:left-8 z-20">
          <Image
            src="/logo-F.png"
            alt="Prolytech"
            width={280}
            height={84}
            className="h-8 sm:h-12 md:h-14 lg:h-10 xl:h-12 w-auto pt-4 lg:pt-4 pl-4 lg:pl-30"
          />
        </div>

        {/* SVG clipPath for the blob, necessary for the video */}
        <svg width="0" height="0">
          <defs>
            <clipPath
              id="blobClip"
              clipPathUnits="objectBoundingBox"
              transform="scale(0.0017, 0.0016)"
            >
              <path d="M81.5702 435.071C122.524 497.307 132.82 569.329 199.46 602.679C276.32 641.145 332.063 562.008 372.109 530.182C404.709 504.272 539.123 512.181 568.025 482.205C616.135 432.309 560.186 383.738 538.312 317.98C521.208 266.562 542.548 186.363 523.087 135.788C495.179 63.2584 475.03 13.9755 398.118 2.63276C332.931 -6.98075 307.096 10.7407 247.773 39.409C176.483 73.8611 73.3626 93.2439 31.9879 160.73C-0.113567 213.091 -10.6617 261.071 12.4247 317.98C33.709 370.448 50.4438 387.769 81.5702 435.071Z" />
            </clipPath>
          </defs>
        </svg>

        {/* Hero Content */}
        <div className="relative z-10 flex flex-col lg:flex-row items-center justify-between h-screen px-4 md:pl-[190px] lg:pl-24 md:pr-4 lg:pr-20 py-8 lg:py-0">
          {/* Left Content */}
          <div className="flex flex-col items-start justify-center lg:justify-end text-left pb-6 md:pb-10 lg:pb-6 mt-0 max-w-full lg:max-w-2xl ml-0 lg:ml-12 order-2 lg:order-1">
  <p className="text-xs font-semibold text-[#05A0E2] uppercase tracking-wide mb-2">
    Our Expertise
  </p>

  <h1 className="text-black text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-extrabold leading-tight">
    <span className="block">Software, Cloud &</span>
    <span className="block">AI Solutions</span>
  </h1>

  <p className="text-gray-500 text-[11px] md:text-base mt-2 leading-relaxed max-w-xl">
      Explore how Prolytech engineers high-performance applications, cloud systems, 
      and AI-powered platforms for scale and innovation.
  </p>

  <Link
    href="/contact"
    className="mt-4 block w-full text-center px-4 py-1.5 text-white font-medium text-xs md:text-sm rounded-full shadow-md transition-all duration-300
      bg-[linear-gradient(to_bottom,#04E6FC,#0664CC)] hover:bg-[linear-gradient(to_bottom,#04c9de,#0559b4)]
      md:inline-block md:w-auto"
  >
    LETS DO IT!
  </Link>
</div>

          {/* Right Side Video with Blob Shape and Background SVG */}
          <div className="flex-1 flex justify-center lg:justify-end items-center relative w-full lg:w-auto mt-15 lg:mt-8 order-1 lg:order-2">
            <div className="absolute z-0
                          top-1/2 left-1/2 transform
                          scale-[0.8] sm:scale-[1] lg:scale-[1.3] xl:scale-[1.4]
                          -translate-y-[calc(50%+16px)] -translate-x-[calc(50%-8px)]">
              <AboutBg className="w-[330px] h-[360px] lg:w-[280px] lg:h-[290px] text-blue-500 lg:ml-10" />
            </div>
            <div
              className="relative z-10 w-full max-w-[290px] sm:max-w-[390px] h-[300px] sm:h-[410px] overflow-hidden"
              style={{
                clipPath: "url(#blobClip)",
                WebkitClipPath: "url(#blobClip)",
              }}
            >
              <div
                className="w-full h-full overflow-hidden"
                style={{
                  clipPath: "url(#blobClip)",
                  WebkitClipPath: "url(#blobClip)",
                }}
              >
                <video
                  src="/assets/services-video.mp4"
                  autoPlay
                  muted
                  loop
                  playsInline
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutHero;